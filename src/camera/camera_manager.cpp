#include "../../include/camera_manager.h"
#include "../../include/common.h"
#include <algorithm>
#include <fstream>

CameraManager::CameraManager() : isRunning_(false) {
    cameras_.resize(Config::CAMERA_COUNT);
    cameraInfos_.resize(Config::CAMERA_COUNT);
    lastFrameTime_.resize(Config::CAMERA_COUNT);
    actualFPS_.resize(Config::CAMERA_COUNT, 0.0);
    
    // 初始化摄像头信息
    for (int i = 0; i < Config::CAMERA_COUNT; ++i) {
        cameraInfos_[i].id = i;
        cameraInfos_[i].status = CameraStatus::DISCONNECTED;
        cameraInfos_[i].width = Config::CAMERA_WIDTH;
        cameraInfos_[i].height = Config::CAMERA_HEIGHT;
        cameraInfos_[i].fps = 0.0;
    }
}

CameraManager::~CameraManager() {
    stopCapture();
    releaseAllCameras();
}

bool CameraManager::initialize() {
    Utils::logInfo("开始初始化摄像头管理器...");
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 检测可用摄像头
    std::vector<int> availableCameras = detectAvailableCameras();
    if (availableCameras.empty()) {
        Utils::logError("未检测到可用摄像头");
        return false;
    }
    
    Utils::logInfo("检测到 " + std::to_string(availableCameras.size()) + " 个可用摄像头");
    
    // 初始化每个摄像头
    bool allSuccess = true;
    for (int i = 0; i < Config::CAMERA_COUNT && i < availableCameras.size(); ++i) {
        if (!initializeCamera(i)) {
            Utils::logError("初始化摄像头 " + std::to_string(i) + " 失败");
            allSuccess = false;
        }
    }
    
    if (!allSuccess) {
        Utils::logWarning("部分摄像头初始化失败，系统将使用可用的摄像头");
    }
    
    Utils::logInfo("摄像头管理器初始化完成");
    return true;
}

bool CameraManager::startCapture() {
    Utils::logInfo("启动摄像头采集...");

    std::lock_guard<std::mutex> lock(mutex_);

    if (isRunning_) {
        Utils::logWarning("摄像头采集已在运行");
        return true;
    }

    // 启动所有可用摄像头
    bool anyStarted = false;
    for (int i = 0; i < Config::CAMERA_COUNT; ++i) {
        if (cameraInfos_[i].status == CameraStatus::CONNECTED) {
            Utils::logInfo("正在打开摄像头 " + std::to_string(i) + "...");

            try {
                // 现在才真正打开摄像头
                cameras_[i] = std::make_unique<cv::VideoCapture>(i);

                if (cameras_[i]->isOpened()) {
                    // 设置摄像头参数
                    cameras_[i]->set(cv::CAP_PROP_FRAME_WIDTH, Config::CAMERA_WIDTH);
                    cameras_[i]->set(cv::CAP_PROP_FRAME_HEIGHT, Config::CAMERA_HEIGHT);
                    cameras_[i]->set(cv::CAP_PROP_FPS, Config::TARGET_FPS);

                    // 验证设置
                    int actualWidth = static_cast<int>(cameras_[i]->get(cv::CAP_PROP_FRAME_WIDTH));
                    int actualHeight = static_cast<int>(cameras_[i]->get(cv::CAP_PROP_FRAME_HEIGHT));
                    double actualFPS = cameras_[i]->get(cv::CAP_PROP_FPS);

                    cameraInfos_[i].width = actualWidth;
                    cameraInfos_[i].height = actualHeight;
                    cameraInfos_[i].fps = actualFPS;

                    cameraInfos_[i].status = CameraStatus::CAPTURING;
                    lastFrameTime_[i] = std::chrono::steady_clock::now();
                    anyStarted = true;

                    Utils::logInfo("✓ 摄像头 " + std::to_string(i) + " 启动成功 (" +
                                  std::to_string(actualWidth) + "x" + std::to_string(actualHeight) +
                                  "@" + std::to_string(actualFPS) + "fps)");
                } else {
                    Utils::logError("无法打开摄像头 " + std::to_string(i));
                    updateCameraStatus(i, CameraStatus::ERROR, "无法打开摄像头");
                }
            } catch (const std::exception& e) {
                Utils::logError("打开摄像头 " + std::to_string(i) + " 时发生异常: " + std::string(e.what()));
                updateCameraStatus(i, CameraStatus::ERROR, "打开异常: " + std::string(e.what()));
            }
        }
    }

    if (anyStarted) {
        isRunning_ = true;
        Utils::logInfo("摄像头采集启动成功");
        return true;
    } else {
        Utils::logError("没有可用的摄像头启动采集");
        return false;
    }
}

void CameraManager::stopCapture() {
    Utils::logInfo("停止摄像头采集...");
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!isRunning_) {
        return;
    }
    
    isRunning_ = false;
    
    // 停止所有摄像头采集
    for (int i = 0; i < Config::CAMERA_COUNT; ++i) {
        if (cameraInfos_[i].status == CameraStatus::CAPTURING) {
            cameraInfos_[i].status = CameraStatus::CONNECTED;
            Utils::logInfo("摄像头 " + std::to_string(i) + " 停止采集");
        }
    }
    
    Utils::logInfo("摄像头采集已停止");
}

bool CameraManager::getFrame(int cameraId, cv::Mat& frame) {
    if (cameraId < 0 || cameraId >= Config::CAMERA_COUNT) {
        Utils::logError("无效的摄像头ID: " + std::to_string(cameraId));
        return false;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!isRunning_ || cameraInfos_[cameraId].status != CameraStatus::CAPTURING) {
        return false;
    }
    
    if (!cameras_[cameraId] || !cameras_[cameraId]->isOpened()) {
        updateCameraStatus(cameraId, CameraStatus::ERROR, "摄像头未打开");
        return false;
    }
    
    bool success = cameras_[cameraId]->read(frame);
    if (success && !frame.empty()) {
        updateFPS(cameraId);
        return true;
    } else {
        updateCameraStatus(cameraId, CameraStatus::ERROR, "读取帧失败");
        return false;
    }
}

bool CameraManager::getAllFrames(std::vector<cv::Mat>& frames) {
    frames.clear();
    frames.resize(Config::CAMERA_COUNT);
    
    bool anySuccess = false;
    for (int i = 0; i < Config::CAMERA_COUNT; ++i) {
        if (getFrame(i, frames[i])) {
            anySuccess = true;
        }
    }
    
    return anySuccess;
}

CameraInfo CameraManager::getCameraInfo(int cameraId) const {
    if (cameraId < 0 || cameraId >= Config::CAMERA_COUNT) {
        CameraInfo info;
        info.id = -1;
        info.errorMsg = "无效的摄像头ID";
        return info;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    return cameraInfos_[cameraId];
}

std::vector<CameraInfo> CameraManager::getAllCameraInfo() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return cameraInfos_;
}

bool CameraManager::isCameraAvailable(int cameraId) const {
    if (cameraId < 0 || cameraId >= Config::CAMERA_COUNT) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    return cameraInfos_[cameraId].status == CameraStatus::CONNECTED ||
           cameraInfos_[cameraId].status == CameraStatus::CAPTURING;
}

double CameraManager::getActualFPS(int cameraId) const {
    if (cameraId < 0 || cameraId >= Config::CAMERA_COUNT) {
        return 0.0;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    return actualFPS_[cameraId];
}

// 私有方法实现
bool CameraManager::initializeCamera(int cameraId) {
    Utils::logInfo("初始化摄像头 " + std::to_string(cameraId) + "...");

    try {
        // 延迟初始化：先不打开摄像头，只标记为已连接
        // 实际的摄像头打开将在startCapture()时进行

        // 设置默认参数
        cameraInfos_[cameraId].width = Config::CAMERA_WIDTH;
        cameraInfos_[cameraId].height = Config::CAMERA_HEIGHT;
        cameraInfos_[cameraId].fps = Config::TARGET_FPS;

        updateCameraStatus(cameraId, CameraStatus::CONNECTED);
        Utils::logInfo("摄像头 " + std::to_string(cameraId) + " 预初始化成功 (延迟打开)");
        return true;

    } catch (const std::exception& e) {
        updateCameraStatus(cameraId, CameraStatus::ERROR, "初始化异常: " + std::string(e.what()));
        return false;
    }
}

void CameraManager::updateCameraStatus(int cameraId, CameraStatus status, const std::string& errorMsg) {
    cameraInfos_[cameraId].status = status;
    cameraInfos_[cameraId].errorMsg = errorMsg;
}

void CameraManager::updateFPS(int cameraId) {
    auto now = std::chrono::steady_clock::now();
    if (lastFrameTime_[cameraId].time_since_epoch().count() > 0) {
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            now - lastFrameTime_[cameraId]).count();
        if (duration > 0) {
            actualFPS_[cameraId] = 1000.0 / duration;
        }
    }
    lastFrameTime_[cameraId] = now;
}

std::vector<int> CameraManager::detectAvailableCameras() {
    std::vector<int> availableCameras;

    Utils::logInfo("开始检测可用摄像头...");

    // 使用系统方法检查设备文件是否存在并且可访问
    for (int i = 0; i < Config::CAMERA_COUNT; ++i) {
        std::string devicePath = "/dev/video" + std::to_string(i);
        std::ifstream device(devicePath);
        if (device.good()) {
            availableCameras.push_back(i);
            Utils::logInfo("✓ 发现可访问的摄像头设备: " + devicePath);
        } else {
            Utils::logWarning("摄像头设备不可访问: " + devicePath);
        }
        device.close();
    }

    // 如果没有找到设备，尝试检查/dev/video0（最常见的情况）
    if (availableCameras.empty()) {
        Utils::logInfo("未找到配置数量的摄像头，尝试检查/dev/video0...");
        std::ifstream device("/dev/video0");
        if (device.good()) {
            availableCameras.push_back(0);
            Utils::logInfo("✓ 找到默认摄像头设备: /dev/video0");
        }
        device.close();
    }

    Utils::logInfo("摄像头检测完成，找到 " + std::to_string(availableCameras.size()) + " 个可用摄像头");
    return availableCameras;
}

bool CameraManager::testCameraCapture(int cameraId) {
    if (!cameras_[cameraId] || !cameras_[cameraId]->isOpened()) {
        return false;
    }
    
    cv::Mat testFrame;
    bool success = cameras_[cameraId]->read(testFrame);
    return success && !testFrame.empty();
}

void CameraManager::releaseCamera(int cameraId) {
    if (cameras_[cameraId]) {
        cameras_[cameraId]->release();
        cameras_[cameraId].reset();
    }
    updateCameraStatus(cameraId, CameraStatus::DISCONNECTED);
}

void CameraManager::releaseAllCameras() {
    for (int i = 0; i < Config::CAMERA_COUNT; ++i) {
        releaseCamera(i);
    }
}
